Instructions for the Competition tab

You need to follow the look and feel of all other tabs. 

The filter sidebar will be the same as in all other tabs.

Now for the graphs. We start with three sets of metrics.

1. Revenue: Revenue growth/decline compared to last year for merchant and competition + comparison with competition
2. Trnasactions: Number of transactions growth/decline compared to last year for merchant and competition + comparison with competition
3. Average Transaction: Average Amount per transaction growth/decline compared to last year for merchant + comparison with competition along with the absolute values for average amount for merchant and competition

In the percentage changes, also show a colored arrow (up and green or down and red) indicating the growth or decline (positive or negative percentage).

Please see the attached image to better understand how they should be depicted.

This will be followed by the WeeklyTurnoverChart.jsx which is already implemented. But you need to change the x-axis so it is custom based on the date range selection, i.e. if the date range is 1/1/23 to 1/31/23, the x-axis should show the first day of each week in that range.

Then we will follow this with the MonthlyTurnoverHeatmap.jsx which is already implemented. You need to have two calendar heatmaps, one for the merchant and one for the competition. It needs to be a scrollable calendar where the dates selected from the date range will be highlighted with the following scaling: variations of red (starting from dark red) for days with low revenue, no color for days without revenue, and variations of green (starting from dark green) for days with high revenue. The color scaling needs to be the same for both merchant and competition. 