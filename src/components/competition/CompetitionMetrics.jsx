import React from 'react';
import { useTranslation } from 'react-i18next';
import { getIcon, getColorClass, formatValue, formatValueDiff } from '../../utils/configHelpers.jsx';

const CompetitionMetricCard = ({ metric }) => {
  const { t } = useTranslation();
  
  const renderArrow = (valueDiff) => {
    const isPositive = valueDiff > 0;
    const arrowColor = isPositive ? 'text-green-500' : 'text-red-500';
    
    return (
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className={`w-4 h-4 ${arrowColor}`} 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        strokeWidth="2" 
        strokeLinecap="round" 
        strokeLinejoin="round"
      >
        {isPositive ? (
          <polyline points="7 14 12 9 17 14" />
        ) : (
          <polyline points="17 10 12 15 7 10" />
        )}
      </svg>
    );
  };

  const renderMetricValue = (data, label) => {
    const isPercentage = metric.valueType === 'percentage';
    const isMixed = metric.valueType === 'mixed';
    
    return (
      <div className="p-4 bg-gray-50/60 rounded-lg">
        <p className="text-xs font-medium text-gray-700 mb-1">{label}</p>
        
        {/* Main Value */}
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg md:text-xl font-bold text-gray-900">
            {isMixed ? (
              `${formatValue(data.absoluteValue, 'currency')}`
            ) : isPercentage ? (
              `${formatValueDiff(data.value, 'percentage')}`
            ) : (
              formatValue(data.value, metric.valueType)
            )}
          </h2>
        </div>
        
        {/* Percentage Change with Arrow */}
        <div className="flex items-center space-x-1">
          {renderArrow(data.valueDiff)}
          <span className={`text-sm font-medium ${
            data.valueDiff > 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {formatValueDiff(data.valueDiff, 'percentage')}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      {/* Header with Icon and Title */}
      <div className="flex items-center mb-4">
        <div className={`p-2 rounded-lg bg-gray-100 mr-3 ${getColorClass(metric.color)}`}>
          {getIcon(metric.icon, "w-5 h-5")}
        </div>
        <h3 className="text-sm font-medium text-gray-900">
          {t(metric.name)}
        </h3>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {renderMetricValue(metric.merchant, t('competition.merchant'))}
        {renderMetricValue(metric.competitor, t('competition.competition'))}
      </div>
    </div>
  );
};

const CompetitionMetrics = ({ metrics }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {metrics.map((metric) => (
        <CompetitionMetricCard key={metric.id} metric={metric} />
      ))}
    </div>
  );
};

export default CompetitionMetrics;
