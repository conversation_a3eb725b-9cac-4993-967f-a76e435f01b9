import React from 'react';
import { useTranslation } from 'react-i18next';
import { getIcon, getColorClass, formatValue, formatValueDiff } from '../../utils/configHelpers.jsx';

const CompetitionMetricCard = ({ metric }) => {
  const { t } = useTranslation();

  const renderArrow = (valueDiff) => {
    const isPositive = valueDiff > 0;
    const arrowColor = isPositive ? 'text-green-500' : 'text-red-500';

    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className={`w-4 h-4 ${arrowColor}`}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        {isPositive ? (
          <polyline points="7 14 12 9 17 14" />
        ) : (
          <polyline points="17 10 12 15 7 10" />
        )}
      </svg>
    );
  };

  // Calculate merchant vs competition percentage (mock calculation)
  const calculateCompetitionComparison = () => {
    const merchantValue = metric.merchant.value;
    const competitorValue = metric.competitor.value;

    if (metric.valueType === 'mixed') {
      // For average transaction amount, compare absolute values
      return ((merchantValue - competitorValue) / competitorValue * 100).toFixed(1);
    } else {
      // For percentage values, show the difference
      return (merchantValue - competitorValue).toFixed(1);
    }
  };

  const competitionComparison = calculateCompetitionComparison();

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      {/* Header with Icon and Title */}
      <div className="flex items-center mb-4">
        <div className={`p-2 rounded-lg bg-gray-100 mr-3 ${getColorClass(metric.color)}`}>
          {getIcon(metric.icon, "w-5 h-5")}
        </div>
        <h3 className="text-sm font-medium text-gray-900">
          {t(metric.name)}
        </h3>
      </div>

      {/* Metrics Grid - Side by Side */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Merchant - Compared to Last Year */}
        <div className="p-3 bg-blue-50/40 rounded-lg">
          <p className="text-xs font-medium text-gray-700 mb-1">
            {t('competition.comparedToLastYear')}
          </p>
          <div className="flex items-center space-x-1">
            {renderArrow(metric.merchant.valueDiff)}
            <span className={`text-lg font-bold ${
              metric.merchant.valueDiff > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {formatValueDiff(metric.merchant.valueDiff, 'percentage')}
            </span>
          </div>
        </div>

        {/* Competition Comparison */}
        <div className="p-3 bg-gray-50/60 rounded-lg">
          <p className="text-xs font-medium text-gray-700 mb-1">
            {t('competition.comparedToCompetition')}
          </p>
          <div className="flex items-center space-x-1 mb-2">
            {renderArrow(parseFloat(competitionComparison))}
            <span className={`text-lg font-bold ${
              parseFloat(competitionComparison) > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {parseFloat(competitionComparison) > 0 ? '+' : ''}{competitionComparison}%
            </span>
          </div>

          {/* Competitor Change */}
          <div className="pt-2 border-t border-gray-200">
            <p className="text-xs font-medium text-gray-500 mb-1">
              {t('competition.competitorChange')}
            </p>
            <div className="flex items-center space-x-1">
              {renderArrow(metric.competitor.valueDiff)}
              <span className={`text-sm font-medium ${
                metric.competitor.valueDiff > 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatValueDiff(metric.competitor.valueDiff, 'percentage')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const CompetitionMetrics = ({ metrics }) => {
  return (
    <div className="space-y-6">
      {metrics.map((metric) => (
        <CompetitionMetricCard key={metric.id} metric={metric} />
      ))}
    </div>
  );
};

export default CompetitionMetrics;
