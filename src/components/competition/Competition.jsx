import React from 'react';
import { useTranslation } from 'react-i18next';
import DashboardHeader from '../dashboard/DashboardHeader.jsx';
import CompetitionMetrics from './CompetitionMetrics.jsx';
import WeeklyTurnoverChart from '../charts/WeeklyTurnoverChart.jsx';
import MonthlyTurnoverHeatmap from '../charts/MonthlyTurnoverHeatmap.jsx';
import { getTabConfig } from '../../utils/configHelpers.jsx';

const Competition = ({ filters }) => {
  const { t } = useTranslation();
  const config = getTabConfig('competition');

  if (!config) {
    return <div>Configuration not found</div>;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* Header */}
      <DashboardHeader 
        title={t(config.title)}
        subtitle={t(config.subtitle)}
      />

      {/* Competition Metrics */}
      <div className="mb-8">
        <CompetitionMetrics metrics={config.metrics} />
      </div>

      {/* Charts Section */}
      <div className="space-y-8">
        {/* Weekly Turnover Chart */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('competition.weeklyTurnover')}
          </h3>
          <WeeklyTurnoverChart filters={filters} />
        </div>

        {/* Monthly Heatmap */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            {t('competition.monthlyHeatmap')}
          </h3>
          <MonthlyTurnoverHeatmap filters={filters} />
        </div>
      </div>
    </div>
  );
};

export default Competition;
