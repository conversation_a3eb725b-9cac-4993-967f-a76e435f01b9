import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay, isAfter } from 'date-fns';
import { el } from 'date-fns/locale';

const MonthlyTurnoverHeatmap = ({ filters }) => {
  const { t } = useTranslation();

  // Initialize current month based on filters
  const getInitialMonth = () => {
    if (filters?.dateRange?.start) {
      return startOfMonth(filters.dateRange.start);
    }
    return new Date();
  };

  const [currentMonth, setCurrentMonth] = useState(getInitialMonth);

  // Update current month when filters change
  useEffect(() => {
    if (filters?.dateRange?.start) {
      setCurrentMonth(startOfMonth(filters.dateRange.start));
    }
  }, [filters?.dateRange?.start]);

  // Generate mock revenue data for each day
  const generateRevenueData = () => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const days = eachDayOfInterval({ start, end });

    const data = {};
    days.forEach(day => {
      const dayKey = format(day, 'yyyy-MM-dd');
      // Generate mock revenue data with some pattern (weekends higher)
      const isWeekend = getDay(day) === 0 || getDay(day) === 6;
      const baseRevenue = isWeekend ? 8000 : 5000;
      const variance = Math.random() * 4000;

      data[dayKey] = {
        merchant: baseRevenue + variance,
        competitor: baseRevenue * 1.2 + variance * 0.8
      };
    });

    return data;
  };

  const revenueData = generateRevenueData();

  // Calculate color scaling based on all revenue values
  const allValues = Object.values(revenueData).flatMap(d => [d.merchant, d.competitor]);
  const minRevenue = Math.min(...allValues);
  const maxRevenue = Math.max(...allValues);

  const getColorIntensity = (revenue) => {
    if (revenue === 0) return 0;
    return (revenue - minRevenue) / (maxRevenue - minRevenue);
  };

  const getHeatmapColor = (revenue, isInRange) => {
    if (revenue === 0 || !isInRange) return '#f3f4f6'; // Light gray for no data or out of range

    const intensity = getColorIntensity(revenue);

    // Better diverging red to green color scale
    if (intensity < 0.5) {
      // Low revenue - red scale (from light red to dark red)
      const redIntensity = intensity * 2; // 0 to 1
      const r = Math.floor(220 + (35 * redIntensity)); // 220 to 255
      const g = Math.floor(53 + (102 * (1 - redIntensity))); // 155 to 53
      const b = Math.floor(69 + (86 * (1 - redIntensity))); // 155 to 69
      return `rgb(${r}, ${g}, ${b})`;
    } else {
      // High revenue - green scale (from light green to dark green)
      const greenIntensity = (intensity - 0.5) * 2; // 0 to 1
      const r = Math.floor(134 - (69 * greenIntensity)); // 134 to 65
      const g = Math.floor(239 - (104 * greenIntensity)); // 239 to 135
      const b = Math.floor(172 - (107 * greenIntensity)); // 172 to 65
      return `rgb(${r}, ${g}, ${b})`;
    }
  };

  // Check if navigation should be allowed
  const canNavigateTo = (targetMonth) => {
    if (!filters?.dateRange?.start || !filters?.dateRange?.end) return true;

    const targetStart = startOfMonth(targetMonth);
    const targetEnd = endOfMonth(targetMonth);
    const filterStart = startOfMonth(filters.dateRange.start);
    const filterEnd = endOfMonth(filters.dateRange.end);

    return targetStart >= filterStart && targetEnd <= filterEnd;
  };

  // Check if a day is within the selected date range
  const isDayInRange = (day) => {
    if (!filters?.dateRange?.start || !filters?.dateRange?.end) return true;

    return day >= filters.dateRange.start && day <= filters.dateRange.end;
  };

  const renderCalendar = (title, dataKey) => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const days = eachDayOfInterval({ start, end });

    // Get first day of month and pad with empty cells (Monday = 0)
    const firstDayOfWeek = getDay(start);
    const mondayFirstDay = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1; // Convert Sunday=0 to Monday=0
    const paddingDays = Array(mondayFirstDay).fill(null);

    const allDays = [...paddingDays, ...days];

    return (
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">{title}</h4>
        <div className="flex flex-col items-center">
          {/* Calendar Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['Δευ', 'Τρι', 'Τετ', 'Πεμ', 'Παρ', 'Σαβ', 'Κυρ'].map(day => (
              <div key={day} className="w-8 h-6 flex items-center justify-center text-xs font-medium text-gray-500">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {allDays.map((day, index) => {
              if (!day) {
                return <div key={index} className="w-8 h-8"></div>;
              }

              const dayKey = format(day, 'yyyy-MM-dd');
              const revenue = revenueData[dayKey]?.[dataKey] || 0;
              const isInRange = isDayInRange(day);
              const isFuture = isAfter(day, new Date());
              const backgroundColor = isFuture ? '#f3f4f6' : getHeatmapColor(revenue, isInRange);

              return (
                <div
                  key={dayKey}
                  className="w-8 h-8 flex items-center justify-center text-xs font-medium rounded border border-gray-200"
                  style={{ backgroundColor }}
                  title={`${format(day, 'dd/MM/yyyy')}: €${revenue.toLocaleString()}`}
                >
                  <span className="text-white font-medium">
                    {format(day, 'd')}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Month Navigation */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <button
          onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
          disabled={!canNavigateTo(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <h3 className="text-lg font-medium text-gray-900 min-w-[150px] text-center">
          {format(currentMonth, 'MMMM yyyy', { locale: el })}
        </h3>

        <button
          onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
          disabled={!canNavigateTo(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Two Calendar Heatmaps */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {renderCalendar(t('competition.merchant'), 'merchant')}
        {renderCalendar(t('competition.competition'), 'competitor')}
      </div>

      {/* Legend */}
      <div className="flex justify-center items-center space-x-4 text-xs text-gray-500 mt-6">
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: 'rgb(255, 53, 69)' }}></div>
          <span>Χαμηλός</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: 'rgb(220, 155, 155)' }}></div>
          <span>Μέτριος</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: '#f3f4f6' }}></div>
          <span>Εκτός Περιόδου</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: 'rgb(134, 239, 172)' }}></div>
          <span>Καλός</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: 'rgb(65, 135, 65)' }}></div>
          <span>Υψηλός</span>
        </div>
      </div>

      {/* Description */}
      <div className="mt-4 text-sm text-gray-500 text-center">
        <p>Τα χρώματα αντιπροσωπεύουν την ένταση του τζίρου για κάθε ημέρα. Η κλίμακα είναι κοινή για έμπορο και ανταγωνισμό.</p>
      </div>
    </div>
  );
};

export default MonthlyTurnoverHeatmap;
