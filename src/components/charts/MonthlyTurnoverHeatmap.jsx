import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, getDay, isSameMonth, isAfter } from 'date-fns';
import { el } from 'date-fns/locale';

const MonthlyTurnoverHeatmap = ({ filters }) => {
  const { t } = useTranslation();
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Generate mock revenue data for each day
  const generateRevenueData = () => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const days = eachDayOfInterval({ start, end });

    const data = {};
    days.forEach(day => {
      const dayKey = format(day, 'yyyy-MM-dd');
      // Generate mock revenue data with some pattern (weekends higher)
      const isWeekend = getDay(day) === 0 || getDay(day) === 6;
      const baseRevenue = isWeekend ? 8000 : 5000;
      const variance = Math.random() * 4000;

      data[dayKey] = {
        merchant: baseRevenue + variance,
        competitor: baseRevenue * 1.2 + variance * 0.8
      };
    });

    return data;
  };

  const revenueData = generateRevenueData();

  // Calculate color scaling based on all revenue values
  const allValues = Object.values(revenueData).flatMap(d => [d.merchant, d.competitor]);
  const minRevenue = Math.min(...allValues);
  const maxRevenue = Math.max(...allValues);

  const getColorIntensity = (revenue) => {
    if (revenue === 0) return 0;
    return (revenue - minRevenue) / (maxRevenue - minRevenue);
  };

  const getHeatmapColor = (revenue) => {
    if (revenue === 0) return 'transparent';

    const intensity = getColorIntensity(revenue);

    if (intensity < 0.3) {
      // Low revenue - red variations
      const redIntensity = Math.floor(intensity * 255 / 0.3);
      return `rgb(${160 + redIntensity}, ${5}, ${45})`;
    } else {
      // High revenue - green variations
      const greenIntensity = Math.floor((intensity - 0.3) * 255 / 0.7);
      return `rgb(${115 - greenIntensity * 0.3}, ${170 + greenIntensity * 0.3}, ${60})`;
    }
  };

  const renderCalendar = (title, dataKey) => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const days = eachDayOfInterval({ start, end });

    // Get first day of month and pad with empty cells
    const firstDayOfWeek = getDay(start);
    const paddingDays = Array(firstDayOfWeek).fill(null);

    const allDays = [...paddingDays, ...days];

    return (
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3 text-center">{title}</h4>
        <div className="flex flex-col items-center">
          {/* Calendar Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['Κυρ', 'Δευ', 'Τρι', 'Τετ', 'Πεμ', 'Παρ', 'Σαβ'].map(day => (
              <div key={day} className="w-8 h-6 flex items-center justify-center text-xs font-medium text-gray-500">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {allDays.map((day, index) => {
              if (!day) {
                return <div key={index} className="w-8 h-8"></div>;
              }

              const dayKey = format(day, 'yyyy-MM-dd');
              const revenue = revenueData[dayKey]?.[dataKey] || 0;
              const isFuture = isAfter(day, new Date());
              const backgroundColor = isFuture ? '#f3f4f6' : getHeatmapColor(revenue);

              return (
                <div
                  key={dayKey}
                  className="w-8 h-8 flex items-center justify-center text-xs font-medium rounded border border-gray-200"
                  style={{ backgroundColor }}
                  title={`${format(day, 'dd/MM/yyyy')}: €${revenue.toLocaleString()}`}
                >
                  <span className={revenue > maxRevenue * 0.5 ? 'text-white' : 'text-gray-700'}>
                    {format(day, 'd')}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Month Navigation */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <button
          onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>

        <h3 className="text-lg font-medium text-gray-900 min-w-[150px] text-center">
          {format(currentMonth, 'MMMM yyyy', { locale: el })}
        </h3>

        <button
          onClick={() => setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))}
          className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* Two Calendar Heatmaps */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {renderCalendar(t('competition.merchant'), 'merchant')}
        {renderCalendar(t('competition.competition'), 'competitor')}
      </div>

      {/* Legend */}
      <div className="flex justify-center items-center space-x-6 text-xs text-gray-500 mt-6">
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: '#A0052D' }}></div>
          <span>Χαμηλός Τζίρος</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: '#f3f4f6' }}></div>
          <span>Χωρίς Δεδομένα</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-sm mr-2" style={{ backgroundColor: '#73AA3C' }}></div>
          <span>Υψηλός Τζίρος</span>
        </div>
      </div>

      {/* Description */}
      <div className="mt-4 text-sm text-gray-500 text-center">
        <p>Τα χρώματα αντιπροσωπεύουν την ένταση του τζίρου για κάθε ημέρα. Η κλίμακα είναι κοινή για έμπορο και ανταγωνισμό.</p>
      </div>
    </div>
  );
};

export default MonthlyTurnoverHeatmap;
