import React from 'react';
import { useTranslation } from 'react-i18next';
import { format, startOfWeek, eachWeekOfInterval } from 'date-fns';
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart, ReferenceLine } from 'recharts';

const WeeklyTurnoverChart = ({ filters }) => {
  const { t } = useTranslation();

  // Generate weekly data based on date range
  const generateWeeklyData = () => {
    if (!filters?.dateRange?.start || !filters?.dateRange?.end) {
      // Default mock data
      return [
        { week: '2024-05-06', weekLabel: '06/05/24', merchantChange: 8.5, competitorChange: 4.2 },
        { week: '2024-05-13', weekLabel: '13/05/24', merchantChange: 12.3, competitorChange: 6.1 },
        { week: '2024-05-20', weekLabel: '20/05/24', merchantChange: -3.2, competitorChange: -1.8 },
        { week: '2024-05-27', weekLabel: '27/05/24', merchantChange: 15.7, competitorChange: 8.9 },
        { week: '2024-06-03', weekLabel: '03/06/24', merchantChange: 6.4, competitorChange: 3.7 },
        { week: '2024-06-10', weekLabel: '10/06/24', merchantChange: -8.1, competitorChange: -5.3 },
        { week: '2024-06-17', weekLabel: '17/06/24', merchantChange: 11.2, competitorChange: 7.8 },
        { week: '2024-06-24', weekLabel: '24/06/24', merchantChange: 4.9, competitorChange: 2.1 }
      ];
    }

    const startDate = startOfWeek(filters.dateRange.start, { weekStartsOn: 1 });
    const endDate = filters.dateRange.end;
    const weeks = eachWeekOfInterval({ start: startDate, end: endDate }, { weekStartsOn: 1 });

    return weeks.map(week => {
      // Generate mock percentage changes with some variation
      const merchantChange = (Math.random() - 0.5) * 30; // -15% to +15%
      const competitorChange = merchantChange * 0.6 + (Math.random() - 0.5) * 10; // Correlated but different

      return {
        week: format(week, 'yyyy-MM-dd'),
        weekLabel: format(week, 'dd/MM/yy'),
        merchantChange: parseFloat(merchantChange.toFixed(1)),
        competitorChange: parseFloat(competitorChange.toFixed(1))
      };
    });
  };

  const weeklyData = generateWeeklyData();

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 mb-2">
            Εβδομάδα: {data.weekLabel}
          </p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'merchantChange' ? 'Έμπορος' : 'Ανταγωνισμός'}: {entry.value > 0 ? '+' : ''}{entry.value}%
            </p>
          ))}
        </div>
      );
    }
    return null;
  };
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Merchant Chart */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-4 text-center">
          {t('competition.merchant')}
        </h4>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={weeklyData}>
            <defs>
              <linearGradient id="positiveAreaMerchant" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#10B981" stopOpacity={0.05}/>
              </linearGradient>
              <linearGradient id="negativeAreaMerchant" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#EF4444" stopOpacity={0.05}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="weekLabel"
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `${value}%`}
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />

            {/* Positive area */}
            <Area
              dataKey="merchantChange"
              stroke="#007B85"
              strokeWidth={2}
              fill="url(#positiveAreaMerchant)"
              fillOpacity={1}
              connectNulls={false}
            />

            {/* Negative area overlay */}
            <Area
              dataKey="merchantChange"
              stroke="#007B85"
              strokeWidth={2}
              fill="url(#negativeAreaMerchant)"
              fillOpacity={1}
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Competition Chart */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-4 text-center">
          {t('competition.competition')}
        </h4>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={weeklyData}>
            <defs>
              <linearGradient id="positiveAreaCompetitor" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#10B981" stopOpacity={0.05}/>
              </linearGradient>
              <linearGradient id="negativeAreaCompetitor" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#EF4444" stopOpacity={0.05}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="weekLabel"
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `${value}%`}
            />
            <Tooltip content={<CustomTooltip />} />
            <ReferenceLine y={0} stroke="#666" strokeDasharray="2 2" />

            {/* Positive area */}
            <Area
              dataKey="competitorChange"
              stroke="#73AA3C"
              strokeWidth={2}
              fill="url(#positiveAreaCompetitor)"
              fillOpacity={1}
              connectNulls={false}
            />

            {/* Negative area overlay */}
            <Area
              dataKey="competitorChange"
              stroke="#73AA3C"
              strokeWidth={2}
              fill="url(#negativeAreaCompetitor)"
              fillOpacity={1}
              connectNulls={false}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default WeeklyTurnoverChart;
